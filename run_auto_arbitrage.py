#!/usr/bin/env python3
"""
自动套利系统主程序 - 简化版
使用工具模块进行交易所实例化和配置加载
"""

import asyncio
import logging
import logging.handlers
from datetime import datetime, timedelta, timezone
import sys
from strategy.auto_arbitrage_controller import AutoArbitrageController
from utils import create_exchange_pair_safe


async def main():
    """主函数 - 运行自动套利系统"""

    # 创建交易所实例
    exchange1, exchange2=create_exchange_pair_safe(exchange1_name='bitget', exchange2_name='binance')
    if not exchange1 or not exchange2:
        return

    # 创建套利配置
    max_concurrent_pairs=8
    arbitrage_config = {
        'max_concurrent_pairs': max_concurrent_pairs,
        'margin_per_pair': 1,
        'leverage': 10,
        'symbols_file_path': 'top_symbols.json',  # 符号分析文件路径
    }

    # 创建自动套利控制器
    print("🚀 创建自动套利控制器...")
    controller = AutoArbitrageController(
        exchange1=exchange1,
        exchange2=exchange2,
        **arbitrage_config
    )
    
    try:
        # 运行系统
        await controller.run()
        
    except KeyboardInterrupt:
        logging.info("用户中断程序")
    except Exception as e:
        logging.error(f"程序异常: {type(e).__name__}: {str(e)}")
    # 注意：不需要在这里调用shutdown()，因为controller.run()内部已经处理了关闭逻辑


if __name__ == '__main__':
    # 配置日志，使用 UTC+8 时区，包含函数名，支持日志轮转
    log_file = 'arbitrage.log'

    # 创建轮转文件处理器：最大10MB，保留5个历史文件
    rotating_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,          # 保留5个历史文件
        encoding='utf-8'
    )

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] [%(funcName)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.StreamHandler(sys.stdout),
            rotating_handler
        ],
        force=True
    )
    logging.Formatter.converter = lambda *args: datetime.now(timezone(timedelta(hours=8))).timetuple()
    
    # 运行主程序
    asyncio.run(main())
