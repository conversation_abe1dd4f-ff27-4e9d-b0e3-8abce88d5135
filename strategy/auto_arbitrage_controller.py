import asyncio
import ccxt.pro as ccxt
import logging
import signal
import sys
import json
import os
from typing import Optional, Dict, List, Tuple
from datetime import datetime, timedelta
from .arbitrage_manager import ArbitrageManager
from .symbol_analyzer import SymbolAnalyzer


class AutoArbitrageController:
    """自动套利控制器 - 统一管理整个自动套利系统"""
    
    def __init__(self, exchange1: ccxt.Exchange, exchange2: ccxt.Exchange,
                 max_concurrent_pairs: int = 5, margin_per_pair: float = 1000,
                 leverage: int = 10,
                 symbols_file_path: str = 'top_symbols.json'):

        self.ex1 = exchange1
        self.ex2 = exchange2
        self.max_concurrent_pairs = max_concurrent_pairs
        self.margin_per_pair = margin_per_pair
        self.leverage = leverage
        self.symbols_file_path = symbols_file_path
        self.update_interval_hours:Optional[int]= None

        # 符号数据（从文件加载）
        self.top_symbols: List[str] = []
        self.symbol_configs: Dict[str, Dict] = {}

        # 组件
        self.arbitrage_manager: Optional[ArbitrageManager] = None
        self.symbol_analyzer: Optional[SymbolAnalyzer] = None

        # 异步任务统一管理
        self.async_tasks: Dict[str, asyncio.Task]= {}

        # 状态
        self.startup_complete = False

        # 更新
        self.last_update_time: Optional[datetime] = None

        # 设置信号处理
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        self._shutdown_event = asyncio.Event()

        def signal_handler(signum, frame):
            logging.info(f"收到信号 {signum}，开始优雅关闭...")


            # 设置标志让主循环退出
            if self.arbitrage_manager:
                self.arbitrage_manager.is_running = False

            # 立即触发关闭事件
            try:
                loop = asyncio.get_running_loop()
                loop.call_soon_threadsafe(self._shutdown_event.set)
                logging.info("关闭信号已发送")
            except RuntimeError:
                # 没有运行的事件循环，直接退出
                logging.info("没有运行的事件循环，强制退出程序")
                import os
                os._exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def cancel_all_tasks(self) -> None:
        """取消所有异步任务"""
        try:
            if not self.async_tasks:
                logging.warning("没有需要取消的任务")
                return

            logging.info(f"取消 {len(self.async_tasks)} 个异步任务...")

            # 取消所有任务
            for task in self.async_tasks.values():
                if not task.done():
                    task.cancel()

            # 等待所有任务完成
            await asyncio.gather(*self.async_tasks.values(), return_exceptions=True)
            self.async_tasks.clear()
            logging.info("所有异步任务已取消")

        except Exception as e:
            logging.error(f"取消异步任务失败: {str(e)}")


    def load_symbol_analysis(self) -> Tuple[List[str], Dict[str, Dict]]:
        """
        加载符号分析数据

        Returns:
            Tuple[List[str], Dict[str, Dict]]: (top_symbols, symbol_configs)
        """
        try:
            if not os.path.exists(self.symbols_file_path):
                logging.error(f"符号分析文件不存在: {self.symbols_file_path}")
                return [], {}

            with open(self.symbols_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            top_symbols = data.get('top_symbols', [])
            analysis_results = data.get('analysis_results', {})
            # 获取更新间隔字段
            self.update_interval_hours = data.get('data_period_hours', 24)


            if not top_symbols:
                logging.warning(f"{self.symbols_file_path} 中没有找到推荐币种")
                return [], {}

            # 创建币种配置
            symbol_configs = self._create_symbol_configs(top_symbols, analysis_results)

            logging.info(f"✅ 成功加载 {len(top_symbols)} 个推荐币种和 {len(analysis_results)} 个分析结果")
            return top_symbols, symbol_configs

        except Exception as e:
            logging.error(f"加载符号分析数据失败: {type(e).__name__}: {str(e)}")
            return [], {}

    def _create_symbol_configs(self, top_symbols: List[str], analysis_results: Dict) -> Dict[str, Dict]:
        """创建币种配置 """
        symbol_configs = {}

        for symbol in top_symbols:
            if symbol in analysis_results:
                result = analysis_results[symbol]

                # 获取分析数据
                avg_spread_pct = result.get('mean_spread')
                max_spread_pct = result.get('max_spread')
                std_spread_pct = result.get('std_spread')
                min_spread_pct = result.get('min_spread')
                volatility_score = result.get('volatility_score')

                # 动态计算阈值
                spread_threshold = avg_spread_pct + std_spread_pct*0.5
                spread_close_threshold = max(
                    min_spread_pct,
                    spread_threshold * 0.01
                )

                symbol_configs[symbol] = {
                    'spread_threshold_pct': spread_threshold,
                    'spread_close_threshold_pct': spread_close_threshold,
                    'volatility_score': volatility_score,
                    'avg_spread_pct': avg_spread_pct,
                    'max_spread_pct': max_spread_pct,
                    'std_spread_pct': std_spread_pct,
                    'min_spread_pct': min_spread_pct,
                }

                logging.info(f"配置 {symbol}: 开仓阈值={spread_threshold:.3f}%, "
                           f"平仓阈值={spread_close_threshold:.3f}%, 平均价差={avg_spread_pct:.3f}%, "
                           f"评分={volatility_score:.2f}")

        logging.info(f"✅ 成功创建 {len(symbol_configs)} 个币种配置")
        return symbol_configs

    async def initialize(self):
        """初始化控制器"""
        try:
            logging.info("="*60)
            logging.info("自动套利系统启动")
            logging.info("="*60)

            # 加载符号分析数据
            logging.info("加载符号分析数据...")
            self.top_symbols, self.symbol_configs = self.load_symbol_analysis()
            if not self.top_symbols:
                raise ValueError("无法加载符号分析数据，请先运行 analyze_symbols.py 生成分析数据")

            self.max_concurrent_pairs=min(self.max_concurrent_pairs, len(self.top_symbols))

            logging.info(f"配置参数:")
            logging.info(f"  - 最大并发币种: {self.max_concurrent_pairs}")
            logging.info(f"  - 每币种保证金: {self.margin_per_pair}")
            logging.info(f"  - 杠杆倍数: {self.leverage}")
            logging.info(f"  - 符号文件路径: {self.symbols_file_path}")
            logging.info(f"  - 更新间隔: {self.update_interval_hours} 小时")
            logging.info("="*60)



            # 加载交易所市场数据
            logging.info("加载交易所市场数据...")
            await self.ex1.load_markets()
            await self.ex2.load_markets()

            # 初始化符号分析器（用于定期更新）
            logging.info("初始化符号分析器...")
            self.symbol_analyzer = SymbolAnalyzer(
                exchange1=self.ex1,
                exchange2=self.ex2,
                analysis_duration_hours=self.update_interval_hours,
                top_symbols_count=max(self.max_concurrent_pairs, len(self.top_symbols))
            )

            # 检查账户余额
            await self._check_account_balance()

            # 初始化套利管理器
            logging.info("初始化套利管理器...")
            self.arbitrage_manager = ArbitrageManager(
                exchange1=self.ex1,
                exchange2=self.ex2,
                max_concurrent_pairs=self.max_concurrent_pairs,
                margin_per_pair=self.margin_per_pair,
                leverage=self.leverage,
                top_symbols=self.top_symbols,
                symbol_configs=self.symbol_configs
            )
            await self.arbitrage_manager.initialize()

            # 记录初始化时间
            self.last_update_time = datetime.now()

            logging.info("系统初始化完成")

        except Exception as e:
            logging.error(f"初始化失败: {type(e).__name__}: {str(e)}")
            raise

    async def _check_account_balance(self):
        """检查账户余额"""
        try:
            logging.info("检查账户余额...")

            # 获取账户余额
            balance1 = await self.ex1.fetch_balance(params={'type': 'swap'})
            balance2 = await self.ex2.fetch_balance(params={'type': 'swap'})

            usdt1 = float(balance1['total'].get('USDT', 0))
            usdt2 = float(balance2['total'].get('USDT', 0))
            total_balance = usdt1 + usdt2

            logging.info(f"账户余额:")
            logging.info(f"  - {self.ex1.id}: {usdt1:.2f} USDT")
            logging.info(f"  - {self.ex2.id}: {usdt2:.2f} USDT")
            logging.info(f"  - 总计: {total_balance:.2f} USDT")

            # 检查余额是否足够
            required_balance = self.max_concurrent_pairs * self.margin_per_pair
            if total_balance < required_balance:
                logging.warning(f"账户余额可能不足！")
                logging.warning(f"需要: {required_balance:.2f} USDT, 当前: {total_balance:.2f} USDT")
            else:
                logging.info(f"账户余额充足，可支持 {self.max_concurrent_pairs} 个币种同时套利")

        except Exception as e:
            logging.error(f"检查账户余额失败: {str(e)}")
            raise

    def get_detailed_position_status(self) -> dict:
        """获取详细的持仓状态信息"""
        status = {
            'active_symbols': [],
            'symbols_with_positions': [],
            'symbols_without_positions': [],
            'position_details': {}
        }

        if not self.arbitrage_manager or not self.arbitrage_manager.active_arbitrages:
            return status

        for symbol, arbitrage in self.arbitrage_manager.active_arbitrages.items():
            status['active_symbols'].append(symbol)

            if self.arbitrage_manager.has_active_positions(arbitrage):
                status['symbols_with_positions'].append(symbol)
                # 获取详细持仓信息
                if hasattr(arbitrage, 'positions') and isinstance(arbitrage.positions, dict):
                    position_info = {}
                    for exchange_id, position in arbitrage.positions.items():
                        if position is not None and isinstance(position, dict):
                            position_info[exchange_id] = {
                                'direction': position.get('direction'),
                                'amount': position.get('amount', 0),
                                'price': position.get('price', 0)
                            }
                    status['position_details'][symbol] = position_info
            else:
                status['symbols_without_positions'].append(symbol)

        return status

    async def update_symbol_analysis(self):
        """更新符号分析数据"""
        try:
            logging.info("开始更新符号分析数据...")

            # 记录更新前的详细持仓状态
            position_status = self.get_detailed_position_status()
            logging.info(f"更新前状态 - 活跃币种: {len(position_status['active_symbols'])}, "
                        f"有持仓: {len(position_status['symbols_with_positions'])}, "
                        f"无持仓: {len(position_status['symbols_without_positions'])}")

            if position_status['symbols_with_positions']:
                logging.info(f"当前有持仓的币种详情:")
                for symbol in position_status['symbols_with_positions']:
                    if symbol in position_status['position_details']:
                        details = position_status['position_details'][symbol]
                        logging.info(f"  {symbol}: {details}")

            if not self.symbol_analyzer:
                logging.error("符号分析器未初始化")
                return False

            # 运行符号分析
            await self.symbol_analyzer.run()

            # 重新加载符号数据
            new_top_symbols, new_symbol_configs = self.load_symbol_analysis()

            if not new_top_symbols:
                logging.error("更新后的符号分析数据为空")
                return False

            # 简化：直接更新符号和配置数据，让rebalance_symbols处理所有的持仓保护逻辑
            old_symbols = set(self.top_symbols)
            new_symbols = set(new_top_symbols)

            # 记录变化（用于日志）
            added_symbols = new_symbols - old_symbols
            removed_symbols = old_symbols - new_symbols

            if added_symbols:
                logging.info(f"新增推荐币种: {list(added_symbols)}")
            if removed_symbols:
                logging.info(f"移除推荐币种: {list(removed_symbols)}")

            # 直接更新数据，不进行复杂的持仓检查和保护逻辑
            # 这些逻辑将由rebalance_symbols统一处理
            self.top_symbols = new_top_symbols
            self.symbol_configs = new_symbol_configs
            self.last_update_time = datetime.now()

            logging.info(f"符号分析数据已更新: {len(self.top_symbols)} 个推荐币种")

            # 更新套利管理器的符号配置
            if self.arbitrage_manager:
                logging.info("更新套利管理器的符号配置...")
                self.arbitrage_manager.top_symbols = self.top_symbols
                self.arbitrage_manager.symbol_configs = self.symbol_configs

                # 触发重新平衡（但不会影响已有持仓）
                await self.arbitrage_manager.rebalance_symbols()

            logging.info(f"符号分析数据更新完成，共 {len(self.top_symbols)} 个符号")
            return True

        except Exception as e:
            logging.error(f"更新符号分析数据失败: {type(e).__name__}: {str(e)}")
            return False

    async def _periodic_update_task(self):
        """定期更新任务"""
        try:
            while True:
                # 计算下次更新时间
                if self.last_update_time:
                    next_update = self.last_update_time + timedelta(hours=self.update_interval_hours)
                    now = datetime.now()

                    if now >= next_update:
                        logging.info("开始定期符号分析更新...")
                        await self.update_symbol_analysis()

                # 每小时检查一次是否需要更新
                await asyncio.sleep(3600)  # 1小时

        except asyncio.CancelledError:
            logging.info("定期更新任务被取消")
            raise  # 重新抛出取消异常
        except Exception as e:
            logging.error(f"定期更新任务异常: {type(e).__name__}: {str(e)}")



    async def run(self):
        """运行自动套利系统"""
        try:

            # 初始化
            await self.initialize()

            self.startup_complete = True

            # 启动套利管理器
            logging.info("="*60)
            logging.info("启动自动套利系统")
            logging.info("="*60)

            # 创建并添加套利管理器任务
            manager_task = asyncio.create_task(self.arbitrage_manager.run())
            self.async_tasks["arbitrage_manager_task"] = manager_task

            # 创建并添加定期更新任务
            update_task = asyncio.create_task(self._periodic_update_task())
            self.async_tasks["update_task"] = update_task

            # 创建并添加关闭监听任务
            shutdown_task = asyncio.create_task(self._shutdown_event.wait())
            self.async_tasks["shutdown_task"] = shutdown_task

            logging.info(f"已启动 {len(self.async_tasks)} 个异步任务")

            # 等待监听关闭任务完成触发
            await asyncio.wait(
                [self.async_tasks["shutdown_task"]],
                return_when=asyncio.FIRST_COMPLETED
            )

            logging.info("收到关闭信号或任务完成，开始清理...")

        except Exception as e:
            logging.error(f"系统运行异常: {type(e).__name__}: {str(e)}")

        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """优雅关闭系统"""

        try:
            logging.info("开始关闭自动套利系统")

            # 取消所有异步任务
            await self.cancel_all_tasks()

            # 关闭套利管理器
            if self.arbitrage_manager:
                await self.arbitrage_manager.close()

            # 关闭交易所连接
            try:
                await self.ex1.close()
                await self.ex2.close()
                logging.info("交易所连接已关闭")
            except Exception as e:
                logging.error(f"关闭交易所连接失败: {str(e)}")

            logging.info("自动套利系统已安全关闭")

        except Exception as e:
            logging.error(f"关闭系统时出错: {str(e)}")
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        status = {

            'startup_complete': self.startup_complete,
            'current_time': datetime.now().isoformat(),
            'async_tasks_count': len(self.async_tasks),
            'arbitrage_manager': None
        }
        
        if self.arbitrage_manager:
            status['arbitrage_manager'] = {
                'active_arbitrages': len(self.arbitrage_manager.active_arbitrages),
                'max_concurrent': self.arbitrage_manager.max_concurrent_pairs,
                'blacklist_count': len(self.arbitrage_manager.blacklist),
                'last_rebalance': (
                    self.arbitrage_manager.last_rebalance.isoformat()
                    if self.arbitrage_manager.last_rebalance else None
                ),
                'performance_summary': self.arbitrage_manager.get_performance_summary()
            }
        
        return status
    
    async def manual_rebalance(self):
        """手动触发重新平衡"""
        if self.arbitrage_manager:
            logging.info("手动触发重新平衡...")
            await self.arbitrage_manager.rebalance_symbols()
        else:
            logging.warning("套利管理器未初始化")
    
    def add_symbol_to_blacklist(self, symbol: str):
        """添加币种到黑名单"""
        if self.arbitrage_manager:
            self.arbitrage_manager.add_to_blacklist(symbol)
        else:
            logging.warning("套利管理器未初始化")
    
    def remove_symbol_from_blacklist(self, symbol: str):
        """从黑名单移除币种"""
        if self.arbitrage_manager:
            self.arbitrage_manager.remove_from_blacklist(symbol)
        else:
            logging.warning("套利管理器未初始化")

    async def manual_update_symbols(self):
        """手动触发符号分析更新"""
        logging.info("手动触发符号分析更新...")
        success = await self.update_symbol_analysis()
        if success:
            logging.info("手动符号分析更新完成")
        else:
            logging.error("手动符号分析更新失败")
        return success

    def get_symbol_update_status(self) -> dict:
        """获取符号更新状态"""
        return {
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'update_interval_hours': self.update_interval_hours,
            'symbols_file_path': self.symbols_file_path,
            'current_symbols_count': len(self.top_symbols),
            'next_update_time': (
                (self.last_update_time + timedelta(hours=self.update_interval_hours)).isoformat()
                if self.last_update_time else None
            )
        }


