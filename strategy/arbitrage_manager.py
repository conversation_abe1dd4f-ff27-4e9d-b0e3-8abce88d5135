import asyncio
import ccxt.pro as ccxt
import logging
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
import json
import os
from .cross_exchange_arbitrage import CrossExchangeArbitrage


class ArbitrageManager:
    """套利管理器 - 管理多个币种的套利实例"""
    
    def __init__(self, exchange1: ccxt.Exchange, exchange2: ccxt.Exchange,
                 max_concurrent_pairs: int = 5, margin_per_pair: float = 1000,
                 leverage: int = 10,
                 top_symbols: list = None, symbol_configs: dict = None):
        self.ex1 = exchange1
        self.ex2 = exchange2
        self.max_concurrent_pairs = max_concurrent_pairs
        self.margin_per_pair = margin_per_pair
        self.leverage = leverage
        self.top_symbols = top_symbols or []
        self.symbol_configs = symbol_configs or {}

        # 套利实例管理
        self.active_arbitrages: Dict[str, CrossExchangeArbitrage] = {}
        # 套利任务管理
        self.arbitrage_tasks: Dict[str, asyncio.Task] = {}
        
        # 状态管理
        self.last_rebalance = None
        self.performance_data: Dict[str, Dict] = {}
        self.blacklist: Set[str] = set()
        
        # 配置文件
        self.config_file = "arbitrage_manager_config.json"
        self.performance_file = "arbitrage_performance.json"

        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置和历史数据"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.blacklist = set(config.get('blacklist', []))
                    self.last_rebalance = config.get('last_rebalance')
                    if self.last_rebalance:
                        self.last_rebalance = datetime.fromisoformat(self.last_rebalance)
                logging.info(f"加载配置: 黑名单 {len(self.blacklist)} 个币种")
            
            if os.path.exists(self.performance_file):
                with open(self.performance_file, 'r') as f:
                    self.performance_data = json.load(f)
                logging.info(f"加载性能数据: {len(self.performance_data)} 个币种")
                
        except Exception as e:
            logging.error(f"加载配置失败: {str(e)}")

    def has_active_positions(self, arbitrage) -> bool:
        """检查套利实例是否有活跃持仓"""
        try:
            if not hasattr(arbitrage, 'positions'):
                logging.debug(f"套利实例 {getattr(arbitrage, 'symbol', 'unknown')} 没有positions属性")
                return False

            positions = arbitrage.positions
            if not isinstance(positions, dict):
                logging.debug(f"套利实例 {getattr(arbitrage, 'symbol', 'unknown')} positions不是字典类型")
                return False

            # 检查每个交易所的持仓
            for exchange_id, position in positions.items():
                if position is not None and isinstance(position, dict):
                    amount = position.get('amount', 0)
                    # 确保amount是数字类型且大于最小阈值
                    try:
                        amount_float = float(amount)
                        if amount_float > 0.0001:  # 设置最小持仓阈值，避免微小残余
                            logging.debug(f"发现 {arbitrage.symbol} 在 {exchange_id} 有持仓: {position}")
                            return True
                    except (ValueError, TypeError):
                        logging.warning(f"套利实例 {arbitrage.symbol} 在 {exchange_id} 的持仓数量无效: {amount}")
                        continue

            return False
        except Exception as e:
            logging.error(f"检查持仓状态失败: {str(e)}")
            # 出错时保守处理，假设有持仓以避免意外关闭
            return True

    def _save_config(self):
        """保存配置"""
        try:
            config = {
                'blacklist': list(self.blacklist),
                'last_rebalance': self.last_rebalance.isoformat() if self.last_rebalance else None,
                'max_concurrent_pairs': self.max_concurrent_pairs,
                'margin_per_pair': self.margin_per_pair,
                'leverage': self.leverage,
            }
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
                
            with open(self.performance_file, 'w') as f:
                json.dump(self.performance_data, f, indent=2)
                
        except Exception as e:
            logging.error(f"保存配置失败: {str(e)}")
    
    async def initialize(self):
        """初始化管理器"""
        try:
            logging.info("初始化套利管理器...")

            # 验证必要的配置
            if not self.top_symbols:
                raise ValueError("必须提供top_symbols配置")

            if not self.symbol_configs:
                raise ValueError("必须提供symbol_configs配置")

            logging.info(f"配置的币种数量: {len(self.top_symbols)}")
            logging.info(f"币种配置数量: {len(self.symbol_configs)}")
            logging.info("套利管理器初始化完成")

        except Exception as e:
            logging.error(f"初始化失败: {type(e).__name__}: {str(e)}")
            raise
    
    async def start_arbitrage_for_symbol(self, symbol: str) -> bool:
        """为指定币种启动套利"""
        try:
            if symbol in self.active_arbitrages:
                logging.warning(f"币种 {symbol} 已在运行套利")
                return False
            
            if symbol in self.blacklist:
                logging.warning(f"币种 {symbol} 在黑名单中，跳过")
                return False
            
            logging.info(f"启动 {symbol} 套利...")

            # 获取币种特定的价差阈值
            symbol_threshold = 0.1
            symbol_close_threshold = 0
            if symbol in self.symbol_configs:
                symbol_threshold = self.symbol_configs[symbol]['spread_threshold_pct']
                logging.info(f"{symbol} 使用动态价差阈值: {symbol_threshold:.3f}%")
                symbol_close_threshold = self.symbol_configs[symbol]['spread_close_threshold_pct']
                logging.info(f"{symbol} 使用动态平仓阈值: {symbol_close_threshold:.3f}%")

            # 创建套利实例
            arbitrage = CrossExchangeArbitrage(
                exchange1=self.ex1,
                exchange2=self.ex2,
                symbol=symbol,
                margin=self.margin_per_pair,
                leverage=self.leverage,
                spread_threshold_pct=symbol_threshold,
                spread_close_threshold_pct=symbol_close_threshold
            )
            
            # 启动套利任务
            task = asyncio.create_task(self._run_arbitrage_with_monitoring(symbol, arbitrage))
            
            self.active_arbitrages[symbol] = arbitrage
            self.arbitrage_tasks[symbol] = task
            
            logging.info(f"成功启动 {symbol} 套利")
            return True
            
        except Exception as e:
            logging.error(f"启动 {symbol} 套利失败: {type(e).__name__}: {str(e)}")
            return False
    
    async def _run_arbitrage_with_monitoring(self, symbol: str, arbitrage: CrossExchangeArbitrage):
        """运行套利并监控性能"""
        start_time = datetime.now()
        
        try:
            # 初始化性能数据
            if symbol not in self.performance_data:
                self.performance_data[symbol] = {
                    'start_time': start_time.isoformat(),
                    'total_trades': 0,
                    'successful_trades': 0,
                    'failed_trades': 0,
                    'total_pnl': 0.0,
                    'error_count': 0,
                    'last_error': None
                }
            
            # 运行套利
            await arbitrage.run()
            
        except Exception as e:
            logging.error(f"{symbol} 套利运行异常: {type(e).__name__}: {str(e)}")
            
            # 记录错误
            self.performance_data[symbol]['error_count'] += 1
            self.performance_data[symbol]['last_error'] = {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'type': type(e).__name__
            }
            
            # 如果错误太多，加入黑名单
            if self.performance_data[symbol]['error_count'] >= 3:
                logging.warning(f"{symbol} 错误次数过多，加入黑名单")
                self.blacklist.add(symbol)
        
        finally:
            # 清理
            if symbol in self.active_arbitrages:
                del self.active_arbitrages[symbol]
            if symbol in self.arbitrage_tasks:
                del self.arbitrage_tasks[symbol]
            
            # 更新性能数据
            self.performance_data[symbol]['end_time'] = datetime.now().isoformat()
            self.performance_data[symbol]['duration_hours'] = (
                datetime.now() - start_time
            ).total_seconds() / 3600
            
            self._save_config()
    
    async def stop_arbitrage_for_symbol(self, symbol: str):
        """停止指定币种的套利"""
        try:
            if symbol not in self.arbitrage_tasks:
                logging.warning(f"币种 {symbol} 没有运行套利")
                return

            logging.info(f"停止 {symbol} 套利...")

            # 取消任务
            task = self.arbitrage_tasks[symbol]
            task.cancel()

            try:
                await task
            except asyncio.CancelledError:
                logging.info(f"{symbol} 套利任务已取消")
            except Exception as e:
                logging.error(f"{symbol} 套利任务异常: {str(e)}")

            # 清理任务和活跃套利记录
            if symbol in self.arbitrage_tasks:
                del self.arbitrage_tasks[symbol]
            if symbol in self.active_arbitrages:
                del self.active_arbitrages[symbol]

            logging.info(f"成功停止 {symbol} 套利")

        except Exception as e:
            logging.error(f"停止 {symbol} 套利失败: {type(e).__name__}: {str(e)}")
    
    async def rebalance_symbols(self):
        """重新平衡币种选择"""
        try:
            logging.info("开始启动套利币种...")

            # 使用预配置的币种列表
            if self.top_symbols:
                logging.info("使用预配置的币种列表进行套利")
                target_symbols = [s for s in self.top_symbols if s not in self.blacklist]


                logging.info(f"目标币种: {target_symbols}")

                # 显示每个币种的配置
                for symbol in target_symbols:
                    if symbol in self.symbol_configs:
                        config = self.symbol_configs[symbol]
                        logging.info(f"{symbol}: 阈值={config['spread_threshold_pct']:.3f}%, "
                                   f"评分={config['volatility_score']:.2f}, "
                                   f"平均价差={config['avg_spread_pct']:.3f}%")
            else:
                # 没有预配置币种，无法启动套利
                error_message = "没有预配置的币种列表，无法启动套利。请确保在初始化时提供top_symbols和symbol_configs参数"
                logging.error(error_message)
                raise Exception(error_message)

            # 重新平衡策略：停止并重启以更新配置（但保护有持仓的币种）
            current_symbols = set(self.active_arbitrages.keys())

            # 检查所有当前运行的币种，找出有持仓的币种
            protected_symbols = set()
            symbols_can_restart = set()

            for symbol in current_symbols:
                if symbol in self.active_arbitrages:
                    arbitrage = self.active_arbitrages[symbol]
                    if self.has_active_positions(arbitrage):
                        protected_symbols.add(symbol)
                        logging.info(f"保护有持仓的币种 {symbol}，不会停止套利")
                    else:
                        symbols_can_restart.add(symbol)
                        logging.info(f"{symbol} 无持仓，可以安全重启以更新配置")

            # 停止所有无持仓的币种（无论是否在新目标列表中）
            for symbol in symbols_can_restart:
                await self.stop_arbitrage_for_symbol(symbol)
                logging.info(f"已停止 {symbol}，准备使用新配置重启")

            # 对于有持仓的币种，更新它们的配置（如果在新目标列表中）
            for symbol in protected_symbols:
                if symbol in target_symbols and symbol in self.symbol_configs:
                    # 更新运行中策略的配置
                    if symbol in self.active_arbitrages:
                        arbitrage = self.active_arbitrages[symbol]
                        new_config = self.symbol_configs[symbol]
                        old_spread_threshold_pct = arbitrage.spread_threshold_pct
                        old_spread_close_threshold_pct = arbitrage.spread_close_threshold_pct
                        arbitrage.spread_threshold_pct = new_config['spread_threshold_pct']
                        arbitrage.spread_close_threshold_pct = new_config['spread_close_threshold_pct']
                        logging.info(f"已更新 {symbol} 的运行配置: 开仓阈值由 {old_spread_threshold_pct:.3f}% 改为{new_config['spread_threshold_pct']:.3f}%"
                                     f"，平仓阈值由 {old_spread_close_threshold_pct:.3f}% 改为{new_config['spread_close_threshold_pct']:.3f}%")
                else:
                    logging.info(f"{symbol} 有持仓但不在新目标列表中，将继续保护运行")

            # 统计持仓币种的分布情况
            if protected_symbols:
                position_in_target = protected_symbols & set(target_symbols)
                position_not_in_target = protected_symbols - set(target_symbols)

                logging.info(f"保护的持仓币种总数: {len(protected_symbols)}")
                if position_in_target:
                    logging.info(f"有持仓且在新推荐中: {list(position_in_target)} (使用新配置)")
                if position_not_in_target:
                    logging.info(f"有持仓但不在新推荐中: {list(position_not_in_target)} (维持原有配置继续运行)")


            # 启动新的套利（包括重启的无持仓币种和全新的币种）
            current_active_symbols = set(self.active_arbitrages.keys())
            symbols_to_start = set(target_symbols) - current_active_symbols

            for symbol in symbols_to_start:
                if len(self.active_arbitrages) < self.max_concurrent_pairs:
                    await self.start_arbitrage_for_symbol(symbol)
                else:
                    logging.warning(f"已达到最大并发数限制，无法启动 {symbol}")
                    break

            self.last_rebalance = datetime.now()
            self._save_config()

            logging.info(f"套利启动完成，当前运行 {len(self.active_arbitrages)} 个币种: "
                        f"{list(self.active_arbitrages.keys())}")

        except Exception as e:
            logging.error(f"启动套利失败: {type(e).__name__}: {str(e)}")
            raise
    


    async def run(self):
        """运行套利管理器主循环"""
        try:
            logging.info("启动套利管理器...")

            # 直接启动套利策略（使用预配置的币种）
            await self.rebalance_symbols()

            logging.info("套利策略启动完成，进入监控模式...")
            logging.info("注意：系统会自动更新币种选择")

            # 简化的监控循环
            while True:
                try:
                    # 监控活跃套利状态
                    await self._monitor_active_arbitrages()

                    # 打印状态报告
                    self._print_status_report()

                    await asyncio.sleep(300) # 每 5 分钟打印一次

                except Exception as e:
                    logging.error(f"监控循环异常: {type(e).__name__}: {str(e)}")


        except asyncio.CancelledError:
            logging.info("套利管理器任务被取消")
            raise  # 重新抛出取消异常
        except Exception as e:
            logging.error(f"套利管理器运行异常: {type(e).__name__}: {str(e)}")
        finally:
            # 关闭资源
            await self.close()



    async def _monitor_active_arbitrages(self):
        """监控活跃套利状态"""
        try:
            # 检查已完成的任务
            completed_symbols = []
            for symbol, task in self.arbitrage_tasks.items():
                if task.done():
                    completed_symbols.append(symbol)
                    try:
                        await task  # 获取异常信息
                    except Exception as e:
                        logging.error(f"{symbol} 套利任务异常结束: {str(e)}")

            # 清理已完成的任务
            for symbol in completed_symbols:
                if symbol in self.arbitrage_tasks:
                    del self.arbitrage_tasks[symbol]
                if symbol in self.active_arbitrages:
                    del self.active_arbitrages[symbol]
                logging.info(f"{symbol} 套利已结束")

        except Exception as e:
            logging.error(f"监控套利状态失败: {str(e)}")

    def _print_status_report(self):
        """打印状态报告"""
        try:
            # 统计有活跃持仓的币种数量
            symbols_with_positions = 0
            position_details = []

            for symbol, arbitrage in self.active_arbitrages.items():
                if self.has_active_positions(arbitrage):
                    symbols_with_positions += 1
                    # 获取持仓详情
                    position_info = []
                    for exchange_id, position in arbitrage.positions.items():
                        if position is not None and position.get('amount', 0) > 0:
                            direction = position.get('direction', 'unknown')
                            amount = position.get('amount', 0)
                            position_info.append(f"{exchange_id}:{direction}({amount:.4f})")

                    if position_info:
                        position_details.append(f"  - {symbol}: {', '.join(position_info)}")

            print("\n" + "="*60)
            print("套利管理器状态报告")
            print("="*60)
            print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"活跃套利数量: {len(self.active_arbitrages)}")
            print(f"有持仓的币种数量: {symbols_with_positions}")  # 新增字段
            print(f"最大并发数: {self.max_concurrent_pairs}")
            print(f"黑名单币种: {len(self.blacklist)}")

            if self.last_rebalance:
                time_since_rebalance = datetime.now() - self.last_rebalance
                hours_since = time_since_rebalance.total_seconds() / 3600
                print(f"距离上次重新平衡: {hours_since:.1f} 小时")

            if self.active_arbitrages:
                print("\n活跃套利:")
                for symbol in self.active_arbitrages:
                    print(f"  - {symbol}")

            # 显示持仓详情
            if position_details:
                print("\n当前持仓详情:")
                for detail in position_details:
                    print(detail)
            else:
                print("\n当前持仓详情: 无活跃持仓")

            print("="*60)

        except Exception as e:
            logging.error(f"打印状态报告失败: {str(e)}")

    async def stop_all_arbitrages_task(self):
        """停止所有套利"""
        try:
            logging.info("停止所有套利...")

            # 停止所有套利任务
            stop_tasks = []
            for symbol in list(self.active_arbitrages.keys()):
                stop_tasks.append(self.stop_arbitrage_for_symbol(symbol))

            if stop_tasks:
                await asyncio.gather(*stop_tasks, return_exceptions=True)

            logging.info("所有套利已停止")

        except Exception as e:
            logging.error(f"停止所有套利失败: {str(e)}")

    def add_to_blacklist(self, symbol: str):
        """添加币种到黑名单"""
        self.blacklist.add(symbol)
        self._save_config()
        logging.info(f"已将 {symbol} 添加到黑名单")

    def remove_from_blacklist(self, symbol: str):
        """从黑名单移除币种"""
        if symbol in self.blacklist:
            self.blacklist.remove(symbol)
            self._save_config()
            logging.info(f"已将 {symbol} 从黑名单移除")

    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {
            'total_symbols_traded': len(self.performance_data),
            'currently_active': len(self.active_arbitrages),
            'blacklisted': len(self.blacklist),
            'total_errors': sum(data.get('error_count', 0) for data in self.performance_data.values()),
            'symbols_with_errors': len([s for s, data in self.performance_data.items()
                                      if data.get('error_count', 0) > 0])
        }
        return summary

    async def close(self):
        """关闭管理器 - 不关闭交易所连接，由上层控制器管理"""

        try:
            logging.info("开始关闭套利管理器")

            self._save_config()

            # 停止所有套利
            await self.stop_all_arbitrages_task()

            logging.info("套利管理器已关闭")

        except Exception as e:
            logging.error(f"关闭管理器失败: {str(e)}")
